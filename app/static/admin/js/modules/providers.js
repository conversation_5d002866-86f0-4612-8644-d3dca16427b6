/**
 * Модуль управления провайдерами
 * Обеспечивает функциональность для работы с SMS провайдерами в админ панели
 */
class ProvidersModule {
    constructor() {
        this.isLoading = false;
        this.currentProviders = [];
        this.editingProviderId = null;
    }

    /**
     * Инициализация модуля провайдеров
     */
    init() {
        console.log('Инициализация модуля провайдеров');
    }

    /**
     * Загрузка списка провайдеров
     */
    async loadProviders() {
        if (this.isLoading) return;
        
        console.log('Загрузка провайдеров');
        this.isLoading = true;

        try {
            // Показываем индикатор загрузки
            this.showLoadingIndicator();

            // Загружаем провайдеров через API
            const response = await apiClient.get(AppConstants.API_ENDPOINTS.PROVIDERS);

            // API возвращает объект с массивом providers
            const providers = response.providers || [];
            this.currentProviders = providers;
            this.renderProviders(providers);

        } catch (error) {
            console.error('Ошибка загрузки провайдеров:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Отображение индикатора загрузки
     */
    showLoadingIndicator() {
        const container = document.getElementById('providers-content');
        if (container) {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <span class="ms-2">Загрузка провайдеров...</span>
                </div>
            `;
        }
    }

    /**
     * Обработка ошибки загрузки
     * @param {Error} error - Ошибка загрузки
     */
    handleLoadError(error) {
        const container = document.getElementById('providers-content');
        if (container) {
            let errorMessage = 'Ошибка загрузки данных';
            
            if (error.message.includes('Не авторизован')) {
                errorMessage = 'Ошибка авторизации. Пожалуйста, войдите в систему заново.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Ошибка соединения с сервером';
            }

            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Ошибка загрузки провайдеров:</strong> ${errorMessage}
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="providersModule.loadProviders()">
                        <i class="bi bi-arrow-clockwise"></i> Повторить
                    </button>
                </div>
            `;
        }

        // Показываем уведомление об ошибке
        if (window.uiManager) {
            uiManager.showError(`Ошибка загрузки провайдеров: ${error.message}`);
        }
    }

    /**
     * Отображение списка провайдеров
     * @param {Array} providers - Массив провайдеров
     */
    renderProviders(providers) {
        const container = document.getElementById('providers-content');
        if (!container) {
            console.error('Контейнер providers-content не найден!');
            return;
        }

        // Если провайдеров нет, показываем специальное сообщение
        if (providers.length === 0) {
            const html = `
                <div class="row">
                    <div class="col-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Провайдеры не настроены</h5>
                                <button class="btn btn-dark btn-sm" onclick="providersModule.loadProviders()" title="Обновить список">
                                    <i class="bi bi-arrow-clockwise"></i> Обновить
                                </button>
                            </div>
                            <div class="card-body text-center py-5">
                                <div class="mb-4">
                                    <i class="bi bi-cloud-slash" style="font-size: 5rem; color: #ffc107;"></i>
                                </div>
                                <h3 class="text-warning mb-3">
                                    <i class="bi bi-exclamation-circle"></i>
                                    Нет настроенных SMS провайдеров
                                </h3>
                                <div class="alert alert-warning mb-4" role="alert">
                                    <h6 class="alert-heading"><i class="bi bi-info-circle"></i> Важно!</h6>
                                    <p class="mb-0">
                                        Для работы системы SMS активаций необходимо добавить хотя бы одного провайдера.<br>
                                        Провайдеры обеспечивают получение виртуальных номеров телефонов для SMS активаций.
                                    </p>
                                </div>

                                <div class="row justify-content-center mb-4">
                                    <div class="col-md-8">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="bi bi-list-check"></i> Поддерживаемые типы провайдеров</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="d-flex align-items-center mb-2">
                                                            <span class="badge bg-info me-2">Firefox API</span>
                                                            <small class="text-muted">Логин + Пароль</small>
                                                        </div>
                                                        <div class="d-flex align-items-center">
                                                            <span class="badge bg-success me-2">SMSActivate API</span>
                                                            <small class="text-muted">API ключ</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="bi bi-check-circle text-success"></i> Автоматическое тестирование<br>
                                                            <i class="bi bi-check-circle text-success"></i> Управление приоритетами<br>
                                                            <i class="bi bi-check-circle text-success"></i> Мониторинг статуса
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button class="btn btn-primary btn-lg px-5 py-3" onclick="providersModule.showProviderModal()">
                                    <i class="bi bi-plus-circle"></i> Добавить первого провайдера
                                </button>

                                <div class="mt-4">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        После добавления провайдера система автоматически протестирует соединение
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Принудительно делаем контейнер видимым
            container.style.display = 'block';
            container.innerHTML = html;
            return;
        }

        // Если провайдеры есть, показываем обычную таблицу

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-cloud"></i> SMS Провайдеры (${providers.length})</h5>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="providersModule.loadProviders()" title="Обновить список">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button class="btn btn-outline-primary me-2" onclick="providersModule.testAllProviders()">
                            <i class="bi bi-check-circle"></i> Тест всех
                        </button>
                        <button class="btn btn-primary" onclick="providersModule.showProviderModal()">
                            <i class="bi bi-plus"></i> Добавить провайдера
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Название</th>
                                    <th>API URL</th>
                                    <th>Формат API</th>
                                    <th>Приоритет</th>
                                    <th>Статус</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${providers.map(provider => `
                                    <tr>
                                        <td>${provider.id}</td>
                                        <td>
                                            <strong>${escapeHtml(provider.name)}</strong>
                                            ${provider.display_name ? `<br><small class="text-muted">${escapeHtml(provider.display_name)}</small>` : ''}
                                        </td>
                                        <td>
                                            <code class="small">${escapeHtml(provider.api_url)}</code>
                                        </td>
                                        <td>
                                            <span class="badge ${provider.api_format === 'firefox_api' ? 'bg-warning text-dark' : 'bg-info'}">${escapeHtml(provider.api_format)}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">${provider.priority}</span>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox"
                                                       id="provider-${provider.id}"
                                                       ${provider.is_active ? 'checked' : ''}
                                                       onchange="providersModule.toggleProvider(${provider.id}, this.checked)">
                                                <label class="form-check-label" for="provider-${provider.id}">
                                                    <span class="badge bg-${getBooleanColor(provider.is_active)}">
                                                        ${formatBoolean(provider.is_active)}
                                                    </span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success me-1"
                                                    onclick="providersModule.testProvider(${provider.id})"
                                                    title="Тестировать">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary me-1"
                                                    onclick="providersModule.editProvider(${provider.id})"
                                                    title="Редактировать">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="providersModule.deleteProvider(${provider.id})"
                                                    title="Удалить">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Принудительно делаем контейнер видимым
        container.style.display = 'block';
        container.innerHTML = html;
    }

    /**
     * Показать модальное окно для создания/редактирования провайдера
     * @param {number|null} providerId - ID провайдера для редактирования (null для создания)
     */
    showProviderModal(providerId = null) {
        this.editingProviderId = providerId;
        const isEditing = providerId !== null;

        const modalHtml = `
            <div class="modal fade" id="providerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-cloud"></i>
                                ${isEditing ? 'Редактировать провайдера' : 'Добавить провайдера'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="providerForm">
                                <!-- Базовые настройки -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="providerName" class="form-label">Название провайдера *</label>
                                        <input type="text" class="form-control" id="providerName" required
                                               placeholder="например: provider3">
                                        <div class="form-text">Уникальное имя провайдера (только латинские буквы и цифры)</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="providerDisplayName" class="form-label">Отображаемое имя *</label>
                                        <input type="text" class="form-control" id="providerDisplayName" required
                                               placeholder="например: Провайдер №3">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="providerApiUrl" class="form-label">API URL *</label>
                                        <input type="url" class="form-control" id="providerApiUrl" required
                                               placeholder="https://api.provider.com/">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="providerApiFormat" class="form-label">Тип API *</label>
                                        <select class="form-select" id="providerApiFormat" required onchange="providersModule.onApiFormatChange(this.value)">
                                            <option value="">Выберите тип API</option>
                                            <option value="smsactivate_api">SMSActivate совместимый</option>
                                            <option value="firefox_api">Firefox совместимый</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Поля для SMSActivate API -->
                                <div id="smsactivate-fields" style="display: none;">
                                    <div class="card border-info mb-3">
                                        <div class="card-header bg-info text-white">
                                            <i class="bi bi-key"></i> Настройки SMSActivate API
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <label for="providerApiKey" class="form-label">API ключ *</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="providerApiKey"
                                                               placeholder="Введите API ключ провайдера">
                                                        <button class="btn btn-outline-secondary" type="button" onclick="providersModule.togglePasswordVisibility('providerApiKey', this)">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </div>
                                                    <div class="form-text">API ключ для авторизации (обычно длинная строка символов)</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Поля для Firefox API -->
                                <div id="firefox-fields" style="display: none;">
                                    <div class="card border-warning mb-3">
                                        <div class="card-header bg-warning text-dark">
                                            <i class="bi bi-person-badge"></i> Настройки Firefox API
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label for="providerUsername" class="form-label">Логин *</label>
                                                    <input type="text" class="form-control" id="providerUsername"
                                                           placeholder="Логин пользователя">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="providerPassword" class="form-label">Пароль *</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="providerPassword"
                                                               placeholder="Пароль пользователя">
                                                        <button class="btn btn-outline-secondary" type="button" onclick="providersModule.togglePasswordVisibility('providerPassword', this)">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Дополнительные настройки -->
                                <div class="card border-secondary mb-3">
                                    <div class="card-header">
                                        <i class="bi bi-gear"></i> Дополнительные настройки
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label for="providerPriority" class="form-label">Приоритет</label>
                                                <input type="number" class="form-control" id="providerPriority" value="100" min="1" max="1000">
                                                <div class="form-text">1 = высший приоритет</div>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="providerTimeout" class="form-label">Таймаут (сек)</label>
                                                <input type="number" class="form-control" id="providerTimeout" value="30" min="5" max="300">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="providerLimit" class="form-label">Лимит запр/мин</label>
                                                <input type="number" class="form-control" id="providerLimit" value="60" min="1" max="1000">
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="providerActive" checked>
                                                    <label class="form-check-label" for="providerActive">
                                                        <i class="bi bi-toggle-on text-success"></i> Провайдер активен
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> Отмена
                            </button>
                            <button type="button" class="btn btn-info" onclick="providersModule.testProviderConnection()">
                                <i class="bi bi-shield-check"></i> Тест API
                            </button>
                            <button type="button" class="btn btn-primary" onclick="providersModule.saveProvider()">
                                <i class="bi bi-check-circle"></i> ${isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('providerModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные провайдера
        if (isEditing) {
            this.loadProviderData(providerId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('providerModal'));
        modal.show();
    }

    /**
     * Динамическое изменение полей формы в зависимости от типа API
     * @param {string} apiFormat - Выбранный формат API
     */
    onApiFormatChange(apiFormat) {

        // Скрываем все специфичные поля
        const smsactivateFields = document.getElementById('smsactivate-fields');
        const firefoxFields = document.getElementById('firefox-fields');

        if (smsactivateFields) smsactivateFields.style.display = 'none';
        if (firefoxFields) firefoxFields.style.display = 'none';

        // Очищаем поля при смене типа API
        const apiKeyField = document.getElementById('providerApiKey');
        const usernameField = document.getElementById('providerUsername');
        const passwordField = document.getElementById('providerPassword');

        if (apiKeyField) apiKeyField.value = '';
        if (usernameField) usernameField.value = '';
        if (passwordField) passwordField.value = '';

        // Показываем нужные поля в зависимости от типа API
        if (apiFormat === 'smsactivate_api') {
            if (smsactivateFields) {
                smsactivateFields.style.display = 'block';
                // Делаем API ключ обязательным
                if (apiKeyField) {
                    apiKeyField.required = true;
                }
            }
        } else if (apiFormat === 'firefox_api') {
            if (firefoxFields) {
                firefoxFields.style.display = 'block';
                // Делаем логин и пароль обязательными
                if (usernameField) usernameField.required = true;
                if (passwordField) passwordField.required = true;
            }
        }
    }

    /**
     * Переключение видимости пароля
     * @param {string} fieldId - ID поля пароля
     * @param {HTMLElement} button - Кнопка переключения
     */
    togglePasswordVisibility(fieldId, button) {
        const field = document.getElementById(fieldId);
        const icon = button.querySelector('i');

        if (field && icon) {
            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'bi bi-eye';
            }
        }
    }

    /**
     * Загрузка данных провайдера для редактирования
     * @param {number} providerId - ID провайдера
     */
    async loadProviderData(providerId) {
        try {
            const provider = this.currentProviders.find(p => p.id === providerId);
            if (provider) {
                // Заполняем базовые поля
                document.getElementById('providerName').value = provider.name;
                document.getElementById('providerDisplayName').value = provider.display_name || provider.name;
                document.getElementById('providerApiUrl').value = provider.api_url;
                document.getElementById('providerApiFormat').value = provider.api_format;

                // Вызываем изменение типа API для показа нужных полей
                this.onApiFormatChange(provider.api_format);

                // Заполняем специфичные поля
                if (provider.api_format === 'smsactivate_api') {
                    const apiKeyField = document.getElementById('providerApiKey');
                    if (apiKeyField) apiKeyField.value = provider.api_key || '';
                } else if (provider.api_format === 'firefox_api') {
                    const usernameField = document.getElementById('providerUsername');
                    const passwordField = document.getElementById('providerPassword');
                    if (usernameField) usernameField.value = provider.username || '';
                    if (passwordField) passwordField.value = provider.password || '';
                }

                // Заполняем дополнительные настройки
                document.getElementById('providerPriority').value = provider.priority || 100;
                document.getElementById('providerTimeout').value = provider.timeout_seconds || 30;
                document.getElementById('providerLimit').value = provider.max_requests_per_minute || 60;
                document.getElementById('providerActive').checked = provider.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных провайдера:', error);
            uiManager.showError('Ошибка загрузки данных провайдера');
        }
    }

    /**
     * Сохранение провайдера (создание или обновление)
     */
    async saveProvider() {
        try {
            const form = document.getElementById('providerForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Собираем базовые данные
            const apiFormat = document.getElementById('providerApiFormat').value;
            const providerData = {
                name: document.getElementById('providerName').value.trim(),
                display_name: document.getElementById('providerDisplayName').value.trim(),
                api_format: apiFormat,
                api_url: document.getElementById('providerApiUrl').value.trim(),
                priority: parseInt(document.getElementById('providerPriority').value),
                timeout_seconds: parseInt(document.getElementById('providerTimeout').value),
                max_requests_per_minute: parseInt(document.getElementById('providerLimit').value),
                is_active: document.getElementById('providerActive').checked,
                settings: {}
            };

            // Добавляем специфичные поля в зависимости от типа API
            if (apiFormat === 'smsactivate_api') {
                const apiKey = document.getElementById('providerApiKey').value.trim();
                if (!apiKey) {
                    uiManager.showError('API ключ обязателен для SMSActivate провайдеров');
                    return;
                }
                providerData.api_key = apiKey;
                providerData.username = null;
                providerData.password = null;
            } else if (apiFormat === 'firefox_api') {
                const username = document.getElementById('providerUsername').value.trim();
                const password = document.getElementById('providerPassword').value.trim();

                if (!username || !password) {
                    uiManager.showError('Логин и пароль обязательны для Firefox провайдеров');
                    return;
                }

                providerData.username = username;
                providerData.password = password;
                providerData.api_key = null;
            } else {
                uiManager.showError('Выберите тип API');
                return;
            }

            let result;
            if (this.editingProviderId) {
                // Обновляем существующего провайдера
                result = await apiClient.put(
                    AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(this.editingProviderId),
                    providerData
                );
                uiManager.showSuccess('Провайдер успешно обновлен');
            } else {
                // Создаем нового провайдера
                result = await apiClient.post(AppConstants.API_ENDPOINTS.PROVIDERS, providerData);
                uiManager.showSuccess('Провайдер успешно создан');
            }

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('providerModal'));
            modal.hide();

            // Перезагружаем список провайдеров
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка сохранения провайдера:', error);
            uiManager.showError(`Ошибка сохранения провайдера: ${error.message}`);
        }
    }

    /**
     * Переключение статуса провайдера
     * @param {number} providerId - ID провайдера
     * @param {boolean} isActive - Новый статус
     */
    async toggleProvider(providerId, isActive) {
        try {
            await apiClient.patch(
                `/api/admin/providers/${providerId}/toggle`,
                { is_active: isActive }
            );
            
            uiManager.showSuccess(`Провайдер ${isActive ? 'активирован' : 'деактивирован'}`);
            
            // Обновляем локальные данные
            const provider = this.currentProviders.find(p => p.id === providerId);
            if (provider) {
                provider.is_active = isActive;
            }

        } catch (error) {
            console.error('Ошибка переключения провайдера:', error);
            uiManager.showError(`Ошибка переключения провайдера: ${error.message}`);
            
            // Возвращаем чекбокс в исходное состояние
            const checkbox = document.getElementById(`provider-${providerId}`);
            if (checkbox) {
                checkbox.checked = !isActive;
            }
        }
    }

    /**
     * Тестирование соединения с провайдером из формы
     */
    async testProviderConnection() {
        try {
            const form = document.getElementById('providerForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Собираем данные для тестирования
            const apiFormat = document.getElementById('providerApiFormat').value;
            const testData = {
                api_url: document.getElementById('providerApiUrl').value.trim(),
                api_format: apiFormat
            };

            // Добавляем учетные данные в зависимости от типа API
            if (apiFormat === 'smsactivate_api') {
                const apiKey = document.getElementById('providerApiKey').value.trim();
                if (!apiKey) {
                    uiManager.showError('Введите API ключ для тестирования');
                    return;
                }
                testData.api_key = apiKey;
            } else if (apiFormat === 'firefox_api') {
                const username = document.getElementById('providerUsername').value.trim();
                const password = document.getElementById('providerPassword').value.trim();

                if (!username || !password) {
                    uiManager.showError('Введите логин и пароль для тестирования');
                    return;
                }

                testData.username = username;
                testData.password = password;
            } else {
                uiManager.showError('Выберите тип API');
                return;
            }

            uiManager.showInfo('Тестирование соединения с провайдером...');

            const result = await apiClient.post('/api/admin/providers/test-connection', testData);

            if (result.success) {
                uiManager.showSuccess(`Соединение успешно! ${result.message || ''}`);
            } else {
                uiManager.showError(`Ошибка соединения: ${result.error || result.message || 'Неизвестная ошибка'}`);
            }

        } catch (error) {
            console.error('Ошибка тестирования соединения:', error);
            uiManager.showError(`Ошибка тестирования: ${error.message}`);
        }
    }

    /**
     * Тестирование провайдера
     * @param {number} providerId - ID провайдера
     */
    async testProvider(providerId) {
        try {
            uiManager.showInfo('Тестирование провайдера...');

            const result = await apiClient.post(
                `/api/admin/providers/${providerId}/test`
            );

            if (result.success) {
                uiManager.showSuccess('Провайдер работает корректно');
            } else {
                uiManager.showError(`Ошибка тестирования: ${result.error || 'Неизвестная ошибка'}`);
            }

        } catch (error) {
            console.error('Ошибка тестирования провайдера:', error);
            uiManager.showError(`Ошибка тестирования провайдера: ${error.message}`);
        }
    }

    /**
     * Тестирование всех провайдеров
     */
    async testAllProviders() {
        try {
            uiManager.showInfo('Тестирование всех провайдеров...');

            const results = await apiClient.post(`/api/admin/providers/test-all`);

            // Показываем результаты в модальном окне
            this.showTestResults(results.results || results);

        } catch (error) {
            console.error('Ошибка тестирования провайдеров:', error);
            uiManager.showError(`Ошибка тестирования провайдеров: ${error.message}`);
        }
    }

    /**
     * Показать результаты тестирования
     * @param {Array} results - Результаты тестирования
     */
    showTestResults(results) {
        const modalHtml = `
            <div class="modal fade" id="testResultsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-check-circle"></i>
                                Результаты тестирования провайдеров
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Провайдер</th>
                                            <th>Статус</th>
                                            <th>Время ответа</th>
                                            <th>Сообщение</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${results.map(result => `
                                            <tr>
                                                <td><strong>${escapeHtml(result.provider_name)}</strong></td>
                                                <td>
                                                    <span class="badge bg-${result.success ? 'success' : 'danger'}">
                                                        ${result.success ? 'Успех' : 'Ошибка'}
                                                    </span>
                                                </td>
                                                <td>${result.response_time ? `${result.response_time}ms` : '-'}</td>
                                                <td>${escapeHtml(result.message || '-')}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('testResultsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('testResultsModal'));
        modal.show();
    }

    /**
     * Редактирование провайдера
     * @param {number} providerId - ID провайдера
     */
    editProvider(providerId) {
        this.showProviderModal(providerId);
    }

    /**
     * Удаление провайдера
     * @param {number} providerId - ID провайдера
     */
    async deleteProvider(providerId) {
        const provider = this.currentProviders.find(p => p.id === providerId);
        if (!provider) return;

        const confirmed = await new Promise((resolve) => {
            uiManager.showConfirmModal(
                'Подтверждение удаления',
                `Вы уверены, что хотите удалить провайдера "${provider.name}"?<br><br>
                <strong>Внимание:</strong> Это действие нельзя отменить.`,
                () => resolve(true),
                () => resolve(false)
            );
        });

        if (!confirmed) return;

        try {
            await apiClient.delete(AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(providerId));
            uiManager.showSuccess('Провайдер успешно удален');
            
            // Перезагружаем список провайдеров
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка удаления провайдера:', error);
            uiManager.showError(`Ошибка удаления провайдера: ${error.message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля провайдеров
window.providersModule = new ProvidersModule();
