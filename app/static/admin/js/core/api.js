/**
 * API клиент для работы с сервером
 * Обеспечивает централизованную работу с HTTP запросами
 */
class ApiClient {
    constructor() {
        this.apiKey = localStorage.getItem('admin_api_key') || '';
        this.baseUrl = '';
    }

    /**
     * Установка API ключа
     * @param {string} apiKey - API ключ администратора
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        localStorage.setItem('admin_api_key', apiKey);
    }

    /**
     * Получение API ключа
     * @returns {string} Текущий API ключ
     */
    getApiKey() {
        return this.apiKey;
    }

    /**
     * Очистка API ключа
     */
    clearApiKey() {
        this.apiKey = '';
        localStorage.removeItem('admin_api_key');
    }

    /**
     * Выполнение HTTP запроса к API
     * @param {string} url - URL для запроса
     * @param {Object} options - Опции запроса (method, headers, body)
     * @returns {Promise} Промис с результатом запроса
     */
    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Добавляем API ключ к запросам админки и провайдеров
        if (this.apiKey && (url.startsWith('/api/admin') || url.startsWith('/admin/') || url.startsWith('/api/providers'))) {
            config.headers['X-API-Key'] = this.apiKey;
        }

        try {
            console.log(`API запрос: ${config.method} ${url}`);
            const response = await fetch(url, config);

            // Проверяем статус ответа
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Не авторизован');
                } else if (response.status === 403) {
                    throw new Error('Доступ запрещен');
                } else if (response.status === 404) {
                    throw new Error('Ресурс не найден');
                } else if (response.status >= 500) {
                    throw new Error('Ошибка сервера');
                } else {
                    const errorText = await response.text();
                    throw new Error(errorText || `HTTP ${response.status}`);
                }
            }

            // Парсим JSON ответ
            const data = await response.json();
            console.log(`API ответ получен:`, data);
            return data;

        } catch (error) {
            console.error(`Ошибка API запроса ${url}:`, error);
            throw error;
        }
    }

    /**
     * GET запрос
     * @param {string} url - URL для запроса
     * @param {Object} params - Параметры запроса
     * @returns {Promise} Промис с результатом запроса
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    }

    /**
     * POST запрос
     * @param {string} url - URL для запроса
     * @param {Object} data - Данные для отправки
     * @returns {Promise} Промис с результатом запроса
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT запрос
     * @param {string} url - URL для запроса
     * @param {Object} data - Данные для отправки
     * @returns {Promise} Промис с результатом запроса
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE запрос
     * @param {string} url - URL для запроса
     * @returns {Promise} Промис с результатом запроса
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    /**
     * PATCH запрос
     * @param {string} url - URL для запроса
     * @param {Object} data - Данные для отправки
     * @returns {Promise} Промис с результатом запроса
     */
    async patch(url, data = {}) {
        return this.request(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    /**
     * Проверка валидности API ключа
     * @param {string} apiKey - API ключ для проверки
     * @returns {Promise<boolean>} true если ключ валидный
     */
    async verifyApiKey(apiKey) {
        try {
            const response = await fetch('/admin/verify-api-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': apiKey
                },
                body: JSON.stringify({})
            });
            return response.ok;
        } catch (error) {
            console.error('Ошибка проверки API ключа:', error);
            return false;
        }
    }
}

// Создаем глобальный экземпляр API клиента
window.apiClient = new ApiClient();
